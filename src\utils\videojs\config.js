/*
 * @Description: 请填写简介
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-16 14:39:04
 * @LastEditors: tian<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-16 15:16:29
 * 
 */
import {getServers} from './icesettings.js';

export async function getServerConfig() {
  const protocolEndPoint = location.origin + '/config';
  // const createResponse = await fetch(protocolEndPoint);
  const data = {
    useWebSocket: true,
    startupMode: 'public',
    logging: 'dev',
  };
  // return await createResponse.json();
  return data
}

export function getRTCConfiguration() {
  let config = {};
  config.sdpSemantics = 'unified-plan';
  config.iceServers = getServers();
  return config;
}

