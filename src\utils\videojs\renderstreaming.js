import Peer from "./peer.js";
import * as Logger from "./logger.js";

/**
 * 生成UUID4格式的唯一标识符
 * 用于创建WebRTC连接的唯一ID
 * @returns {string} 生成的UUID字符串
 */
function uuid4() {
  var temp_url = URL.createObjectURL(new Blob());
  var uuid = temp_url.toString();
  URL.revokeObjectURL(temp_url);
  return uuid.split(/[:/]/g).pop().toLowerCase(); // 移除前缀
}

/**
 * WebRTC渲染流管理类
 * 负责管理WebRTC连接的完整生命周期，包括：
 * - 信令处理和连接建立
 * - 媒体轨道管理
 * - 数据通道创建
 * - 连接状态监控和错误处理
 * - 资源清理和连接断开
 */
export class RenderStreaming {
  /**
   * 构造函数
   * @param {Object} signaling - 信令处理实例（WebSocket或HTTP）
   * @param {RTCConfiguration} config - WebRTC配置对象
   */
  constructor(signaling, config) {
    // WebRTC连接相关
    this._peer = null;                    // RTCPeerConnection实例
    this._connectionId = null;            // 当前连接ID
    this._isConnected = false;            // 连接状态标志
    this._connectionStartTime = null;     // 连接开始时间

    // 回调函数定义（提供默认实现）
    this.onConnect = function (connectionId) {
      Logger.log(`WebRTC连接已建立，连接ID: ${connectionId}`);
    };
    this.onDisconnect = function (connectionId) {
      Logger.log(`WebRTC连接已断开，连接ID: ${connectionId}`);
    };
    this.onGotOffer = function (connectionId) {
      Logger.log(`收到Offer信令，连接ID: ${connectionId}`);
    };
    this.onGotAnswer = function (connectionId) {
      Logger.log(`收到Answer信令，连接ID: ${connectionId}`);
    };
    this.onTrackEvent = function (data) {
      Logger.log(`收到媒体轨道事件，数据: ${JSON.stringify(data)}`);
    };
    this.onAddChannel = function (data) {
      Logger.log(`添加数据通道事件，数据: ${JSON.stringify(data)}`);
    };

    // 保存配置
    this._config = config;
    this._signaling = signaling;

    // 绑定信令事件监听器
    this._setupSignalingEventListeners();

    Logger.log('RenderStreaming实例已创建');
  }

  /**
   * 设置信令事件监听器
   * 绑定各种信令事件的处理函数
   * @private
   */
  _setupSignalingEventListeners() {
    this._signaling.addEventListener('connect', this._onConnect.bind(this));
    this._signaling.addEventListener('disconnect', this._onDisconnect.bind(this));
    this._signaling.addEventListener('offer', this._onOffer.bind(this));
    this._signaling.addEventListener('answer', this._onAnswer.bind(this));
    this._signaling.addEventListener('candidate', this._onIceCandidate.bind(this));

    Logger.log('信令事件监听器已设置');
  }

  /**
   * 处理连接建立事件
   * 当信令服务器确认连接建立时调用
   * @param {CustomEvent} e - 连接事件对象
   * @private
   */
  async _onConnect(e) {
    const data = e.detail;
    Logger.log(`收到连接建立事件，连接ID: ${data.connectionId}`);

    // 验证连接ID是否匹配
    if (this._connectionId === data.connectionId) {
      try {
        // 记录连接开始时间
        this._connectionStartTime = Date.now();

        // 准备WebRTC对等连接
        this._preparePeerConnection(this._connectionId, data.polite);

        // 更新连接状态
        this._isConnected = true;

        // 调用用户定义的连接回调
        this.onConnect(data.connectionId);

        Logger.log(`WebRTC连接建立成功，连接ID: ${data.connectionId}`);
      } catch (error) {
        Logger.error(`建立WebRTC连接失败: ${error.message}`);
        await this._handleConnectionError(error);
      }
    } else {
      Logger.warn(`连接ID不匹配，期望: ${this._connectionId}，实际: ${data.connectionId}`);
    }
  }

  /**
   * 处理连接断开事件
   * 当连接断开时清理所有相关资源
   * @param {CustomEvent} e - 断开事件对象
   * @private
   */
  async _onDisconnect(e) {
    const data = e.detail;
    Logger.log(`收到连接断开事件，连接ID: ${data.connectionId}`);

    // 验证连接ID是否匹配
    if (this._connectionId === data.connectionId) {
      try {
        // 计算连接持续时间
        const connectionDuration = this._connectionStartTime ?
          Date.now() - this._connectionStartTime : 0;
        Logger.log(`连接持续时间: ${connectionDuration}ms`);

        // 调用用户定义的断开回调
        this.onDisconnect(data.connectionId);

        // 清理WebRTC连接
        await this._cleanupPeerConnection();

        // 更新连接状态
        this._isConnected = false;
        this._connectionStartTime = null;

        Logger.log(`WebRTC连接断开完成，连接ID: ${data.connectionId}`);
      } catch (error) {
        Logger.error(`处理连接断开失败: ${error.message}`);
      }
    }
  }

  /**
   * 清理WebRTC对等连接
   * 安全地关闭连接并释放资源
   * @private
   */
  async _cleanupPeerConnection() {
    if (this._peer) {
      try {
        // 关闭所有数据通道
        const dataChannels = this._peer.getDataChannels ? this._peer.getDataChannels() : [];
        dataChannels.forEach(channel => {
          if (channel.readyState === 'open') {
            channel.close();
          }
        });

        // 停止所有发送器的轨道
        const senders = this._peer.pc ? this._peer.pc.getSenders() : [];
        senders.forEach(sender => {
          if (sender.track) {
            sender.track.stop();
          }
        });

        // 关闭对等连接
        this._peer.close();
        this._peer = null;

        Logger.log('WebRTC对等连接已清理');
      } catch (error) {
        Logger.error(`清理WebRTC连接失败: ${error.message}`);
      }
    }
  }

  /**
   * 处理连接错误
   * 统一的错误处理机制
   * @param {Error} error - 错误对象
   * @private
   */
  async _handleConnectionError(error) {
    Logger.error(`WebRTC连接错误: ${error.message}`);

    // 清理资源
    await this._cleanupPeerConnection();

    // 重置状态
    this._isConnected = false;
    this._connectionStartTime = null;

    // 可以在这里添加重连逻辑
    // this._attemptReconnection();
  }

  async _onOffer(e) {
    const offer = e.detail;
    if (!this._peer) {
      this._preparePeerConnection(offer.connectionId, offer.polite);
    }
    const desc = new RTCSessionDescription({ sdp: offer.sdp, type: "offer" });
    try {
      await this._peer.onGotDescription(offer.connectionId, desc);
    } catch (error) {
      Logger.warn(`Error happen on GotDescription that description.\n Message: ${error}\n RTCSdpType:${desc.type}\n sdp:${desc.sdp}`);
      return;
    }
  }

  async _onAnswer(e) {
    const answer = e.detail;
    const desc = new RTCSessionDescription({ sdp: answer.sdp, type: "answer" });
    if (this._peer) {
      try {
        await this._peer.onGotDescription(answer.connectionId, desc);
      } catch (error) {
        Logger.warn(`Error happen on GotDescription that description.\n Message: ${error}\n RTCSdpType:${desc.type}\n sdp:${desc.sdp}`);
        return;
      }
    }
  }

  async _onIceCandidate(e) {
    const candidate = e.detail;
    const iceCandidate = new RTCIceCandidate({ candidate: candidate.candidate, sdpMid: candidate.sdpMid, sdpMLineIndex: candidate.sdpMLineIndex });
    if (this._peer) {
      await this._peer.onGotCandidate(candidate.connectionId, iceCandidate);
    }
  }

  /**
   * if not set argument, a generated uuid is used.
   * @param {string | null} connectionId
   */
  async createConnection(connectionId) {
    this._connectionId = connectionId ? connectionId : uuid4();
    await this._signaling.createConnection(this._connectionId);
  }

  async deleteConnection() {
    await this._signaling.deleteConnection(this._connectionId);
  }

  _preparePeerConnection(connectionId, polite) {
    if (this._peer) {
      Logger.log('Close current PeerConnection');
      this._peer.close();
      this._peer = null;
    }

    // Create peerConnection with proxy server and set up handlers
    this._peer = new Peer(connectionId, polite, this._config);
    this._peer.addEventListener('disconnect', () => {
      this.onDisconnect(`Receive disconnect message from peer. connectionId:${connectionId}`);
    });
    this._peer.addEventListener('trackevent', (e) => {
      const data = e.detail;
      this.onTrackEvent(data);
    });
    this._peer.addEventListener('adddatachannel', (e) => {
      const data = e.detail;
      this.onAddChannel(data);
    });
    this._peer.addEventListener('ongotoffer', (e) => {
      const id = e.detail.connectionId;
      this.onGotOffer(id);
    });
    this._peer.addEventListener('ongotanswer', (e) => {
      const id = e.detail.connectionId;
      this.onGotAnswer(id);
    });
    this._peer.addEventListener('sendoffer', (e) => {
      const offer = e.detail;
      this._signaling.sendOffer(offer.connectionId, offer.sdp);
    });
    this._peer.addEventListener('sendanswer', (e) => {
      const answer = e.detail;
      this._signaling.sendAnswer(answer.connectionId, answer.sdp);
    });
    this._peer.addEventListener('sendcandidate', (e) => {
      const candidate = e.detail;
      this._signaling.sendCandidate(candidate.connectionId, candidate.candidate, candidate.sdpMid, candidate.sdpMLineIndex);
    });
    return this._peer;
  }

  /**
   * @returns {Promise<RTCStatsReport> | null}
   */
  async getStats() {
    return await this._peer.getStats(this._connectionId);
  }

  /**
   * @param {string} label
   * @returns {RTCDataChannel | null}
   */
  createDataChannel(label) {
    return this._peer.createDataChannel(this._connectionId, label);
  }

  /**
   * @param {MediaStreamTrack} track
   * @returns {RTCRtpSender | null}
   */
  addTrack(track) {
    return this._peer.addTrack(this._connectionId, track);
  }

  /**
   * @param {MediaStreamTrack | string} trackOrKind
   * @param {RTCRtpTransceiverInit | null} init
   * @returns {RTCRtpTransceiver | null}
   */
  addTransceiver(trackOrKind, init) {
    return this._peer.addTransceiver(this._connectionId, trackOrKind, init);
  }


  /**
   * @returns {RTCRtpTransceiver[] | null}
   */
  getTransceivers() {
    return this._peer.getTransceivers(this._connectionId);
  }

  async start() {
    await this._signaling.start();
  }

  /**
   * 停止渲染流并清理所有资源
   * 这是解决资源泄漏和连接残留问题的关键方法
   */
  async stop() {
    Logger.log('开始停止渲染流');

    try {
      // 清理WebRTC连接
      await this._cleanupPeerConnection();

      // 停止信令服务
      if (this._signaling) {
        await this._signaling.stop();
        this._signaling = null;
        Logger.log('信令服务已停止');
      }

      // 重置所有状态
      this._connectionId = null;
      this._isConnected = false;
      this._connectionStartTime = null;

      Logger.log('渲染流停止完成');
    } catch (error) {
      Logger.error(`停止渲染流失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取连接状态
   * @returns {boolean} 当前连接状态
   */
  isConnected() {
    return this._isConnected;
  }

  /**
   * 获取连接持续时间
   * @returns {number} 连接持续时间（毫秒），如果未连接则返回0
   */
  getConnectionDuration() {
    return this._connectionStartTime ? Date.now() - this._connectionStartTime : 0;
  }

  /**
   * 获取连接ID
   * @returns {string|null} 当前连接ID
   */
  getConnectionId() {
    return this._connectionId;
  }
}
