import {ElMessage} from 'element-plus';
/*
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: tzs
 * @LastEditTime: 2025-04-15 11:16:20
 */
import service from '@/utils/request';
import {createApp} from 'vue';
import '@/style.css';
import App from '@/App.vue';
import router from '@/router/index';
import pinia from '@/pinia/index';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import '@/assets/iconfont/iconfont.css';
import '@/assets/css/global.css';
import '@/assets/css/main.css';
import '@/assets/css/element_ui.less';

import '@/assets/css/mapbox_style/maplibre-gl.css';
import '@/assets/css/mapbox_style/mapbox-gl-draw_v1.4.2.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import draggable from '@/utils/drag';
const app = createApp(App);
// 挂载element-icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.directive('draggable', draggable);
// //部署时
// window.addEventListener(
//   'message',
//   e => {
//     if (e.data?.token) {
//       service.defaults.headers['X-token'] = e.data.token;
//       app.use(router);
//       app.use(pinia);
//       app.use(ElementPlus, {locale: zhCn});
//       app.mount('#app');
//     }
//   },
//   false
// );
//开发时
service.defaults.headers['X-token'] = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjaHVhbmdxaSIsInN1YiI6IjMiLCJleHAiOjIwMTczNjA3NTYsImlhdCI6MTcwMjAwMDc1Nn0.EVVr2K8fu7x5ibYiwapcH21FnbYVmXOcxk7RHzPhm8c';
app.use(router);
app.use(pinia);
app.use(ElementPlus, {locale: zhCn});
app.mount('#app');
