{"name": "vite_template", "private": true, "version": "0.0.0", "type": "commonjs", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.ts,.vue --ignore-path .gitignore --fix src", "style": "stylelint --fix \"src/**/*.{vue,scss,css,less}\"", "style:html": "stylelint --fix \"**/index.html\"", "dts": "vue-tsc --declaration --emitDeclarationOnly "}, "dependencies": {"@antv/g6": "^4.1.1", "sortablejs": "^1.15.2", "@element-plus/icons-vue": "^2.3.1", "js-base64": "^3.6.1", "@mapbox/mapbox-gl-draw": "^1.4.3", "@turf/turf": "^6.5.0", "axios": "^1.4.0", "browserify": "^17.0.0", "echarts": "^5.4.3", "element-plus": "^2.6.0", "mapbox-gl-controls": "^2.3.5", "mapbox-gl-draw-circle": "^1.1.2", "mapbox-gl-draw-rectangle-restrict-area": "^3.1.5", "mitt": "^3.0.1", "moment": "^2.29.4", "numeral": "^2.0.6", "pinia": "^2.0.36", "uuid": "^9.0.0", "vite-plugin-commonjs": "^0.10.1", "vue": "^3.2.45", "vue-router": "^4.1.6"}, "devDependencies": {"@types/node": "^18.14.1", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@vitejs/plugin-vue": "^4.6.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.6.0", "eslint-define-config": "^1.20.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.11.1", "less": "^4.1.3", "less-loader": "^11.1.0", "postcss-less": "^6.0.0", "prettier": "^2.8.4", "stylelint": "^14.16.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended-less": "^1.0.4", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^6.0.1", "typescript": "^4.9.3", "vite": "^4.1.0", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^1.0.24"}}