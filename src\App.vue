<!--
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: tzs
 * @LastEditTime: 2024-06-24 11:39:15
-->
<script setup lang="ts">
import Header from '@/components/HeaderContainer.vue';
import Footer from '@/components/FooterContainer.vue';
</script>

<template>
  <div class="content">
    <!-- <Header /> -->
    <router-view />
    <!-- <Footer /> -->
  </div>
</template>

<style lang="less" scoped>
.content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
