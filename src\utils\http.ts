/*
 * @Author: 老范
 * @Date: 2023-05-09 11:06:40
 * @LastEditors: tzs
 * @LastEditTime: 2024-03-18 10:05:19
 * @Description: 请填写简介
 */
import service from '@/utils/request';
interface methodConfigType {
  method: string;
  url: string;
  data?: object;
  params?: object;
  headers?: object;
  urlType?: string;
}
const http = {
  /**
   * methods: 请求
   * @param url 请求地址
   * @param params 请求参数
   */
  get(url: string, params?: object, urlType?: string) {
    const config: methodConfigType = {
      method: 'get',
      url: url,
    };
    if (params) config.params = params;
    if (urlType) config.urlType = urlType;
    return service(config);
  },
  post(url: string, params?: object, urlType?: string) {
    const config: methodConfigType = {
      method: 'post',
      url: url,
    };
    if (params) config.data = params;
    if (urlType) config.urlType = urlType;
    return service(config);
  },
  put(url: string, params?: object) {
    const config: methodConfigType = {
      method: 'put',
      url: url,
    };
    if (params) config.data = params;
    return service(config);
  },
  delete(url: string, params?: object) {
    const config: methodConfigType = {
      method: 'delete',
      url: url,
    };
    if (params) config.data = params;
    return service(config);
  },
};
//导出
export default http;
