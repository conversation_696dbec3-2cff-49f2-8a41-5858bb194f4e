/**
 * @description: 深拷贝
 * @return {*}
 */
export const deepClone = <T>(data: T): T => {
  return JSON.parse(JSON.stringify(data));
};

/**
 * 合并相同数据，导出合并列所需的方法
 * @param {Object} data
 * @param {Object} rowspanArray
 */
export const getRowspanMethod = (data: any[], rowspanArray: any[]) => {
  // 要合并的列
  const rowspanNumObject: any = {};

  // 初始化 rowspanNumObject
  rowspanArray.forEach(item => {
    rowspanNumObject[item] = new Array(data.length).fill(1, 0, 1).fill(0, 1);
    rowspanNumObject[`${item}-index`] = 0;
  });

  // 计算相关的合并信息
  for (let i = 1; i < data.length; i++) {
    rowspanArray.forEach(key => {
      const index = rowspanNumObject[`${key}-index`];
      if (data[i][key] === data[i - 1][key]) {
        rowspanNumObject[key][index]++;
      } else {
        rowspanNumObject[`${key}-index`] = i;
        rowspanNumObject[key][i] = 1;
      }
    });
  }

  // 提供合并的方法并导出
  const spanMethod = function ({row, column, rowIndex, columnIndex}: any) {
    if (rowspanArray.includes(column['property'])) {
      const rowspan = rowspanNumObject[column['property']][rowIndex];
      if (rowspan > 0) {
        return {rowspan: rowspan, colspan: 1};
      }
      return {rowspan: 0, colspan: 0};
    }
    return {rowspan: 1, colspan: 1};
  };

  return spanMethod;
};

/**
 * 处理想定的试验参数tree数据
 * @param {Object} data
 */
export const setEntityName = (data: any[], entityName?: string) => {
  data.forEach(i => {
    // 后端给的数据id没有分类，新增customId用作唯一标识，各级数据添加entityName字段
    if (entityName) {
      i.entityName = entityName;
      i.customId = i.entityName + '+' + i.id;
    }
    if (!i.params) return;
    if (i.macroParams) {
      // 给分组设置id
      i.params.forEach(v => {
        v.id = i.name + '_' + v.name;
        v.customId = i.name + '+' + i.name;
      });
      // 实体的实体名称就是自身的名称
      i.entityName = i.name;
      i.customId = i.name + '+' + i.id;
    }
    i.params.forEach(v => {
      v.entityName = i.entityName;
      v.customId = i.entityName + '+' + i.id;

      if (v.params) {
        setEntityName(v.params, v.entityName);
      }
    });
  });
};
