/*
 * @Author: ylx
 * @Description:试验设计数据
 * @Date: 2023-11-07 09:20:11
 * @LastEditors: tzs
 * @LastEditTime: 2024-08-26 16:02:42
 * @FilePath: \afsim-experiment-management\src\pinia\ExperimentsData.ts
 */
import {defineStore} from 'pinia';
import type {experimentsDataType, experimentsResultType} from '@/types/index';

const experimentsData: experimentsDataType = {id: -1, executionNum: -1};
const experimentsResult: experimentsResultType = {
  designMethod: '', //样本的生成算法
  sampleList: [], //样本列表
  sampleColunm: [], //样本表格的列渲染数据
  sampleNum: 0, //样本数量
  monteCarloCount: 1, //蒙特卡洛仿真次数
};

export const useExperiments = defineStore('ExperimentsData', {
  state: () => {
    return {
      experimentsData, //试验设计因子等数据
      experimentsResult, //试验设计的结果数据
      isRunning: false, // 是否正在运行
    };
  },
  actions: {
    setExperimentsData(data: experimentsDataType) {
      this.experimentsData = data;
    },
    setExperimentsResult(data: experimentsResultType) {
      this.experimentsResult = data;
    },
  },
});
