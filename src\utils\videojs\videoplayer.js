import {Observer, Sender} from './sender.js';
import {InputRemoting} from './inputremoting.js';

/**
 * 视频播放器类
 * 负责管理WebRTC视频流的播放、显示和用户交互
 * 包含完整的生命周期管理：创建、播放、调整大小、销毁
 */
export class VideoPlayer {
  constructor() {
    // DOM元素引用
    this.playerElement = null;           // 播放器容器元素
    this.lockMouseCheck = null;          // 鼠标锁定复选框
    this.videoElement = null;            // 视频元素
    this.fullScreenButtonElement = null; // 全屏按钮元素

    // 输入处理相关
    this.inputRemoting = null;           // 输入远程控制实例
    this.sender = null;                  // 输入发送器实例
    this.inputSenderChannel = null;      // 输入数据通道

    // 视频流管理
    this.currentMediaStream = null;      // 当前媒体流
    this.videoTracks = [];               // 视频轨道数组

    // 视频尺寸和缩放参数
    this._videoScale = 1;                // 视频缩放比例
    this._videoOriginX = 0;              // 视频原点X坐标
    this._videoOriginY = 0;              // 视频原点Y坐标

    console.log('VideoPlayer实例已创建');
  }

  /**
   * 创建视频播放器
   * 初始化视频元素、全屏按钮和事件监听器
   * @param {Element} playerElement - 播放器父容器元素
   * @param {HTMLInputElement} lockMouseCheck - 鼠标锁定复选框元素
   */
  createPlayer(playerElement, lockMouseCheck) {
    console.log('开始创建视频播放器');

    // 如果已存在播放器，先清理
    if (this.videoElement) {
      console.log('检测到现有播放器，先进行清理');
      this._cleanupVideoElement();
    }

    // 设置基本属性
    this.playerElement = playerElement;
    this.lockMouseCheck = lockMouseCheck;

    // 创建视频元素
    this._createVideoElement();

    // 创建全屏按钮（暂时注释掉）
    this._createFullscreenButton();

    // 添加事件监听器
    this._addEventListeners();

    console.log('视频播放器创建完成');
  }

  /**
   * 创建视频元素
   * 设置视频元素的基本属性和媒体流
   * @private
   */
  _createVideoElement() {
    this.videoElement = document.createElement('video');
    this.videoElement.id = 'Video';
    this.videoElement.style.touchAction = 'none';
    this.videoElement.playsInline = true;
    this.videoElement.muted = true;

    // 创建新的媒体流
    this.currentMediaStream = new MediaStream();
    this.videoElement.srcObject = this.currentMediaStream;

    // 添加视频元素到容器
    this.playerElement.appendChild(this.videoElement);

    console.log('视频元素已创建并添加到DOM');
  }

  /**
   * 创建全屏按钮
   * @private
   */
  _createFullscreenButton() {
    this.fullScreenButtonElement = document.createElement('img');
    this.fullScreenButtonElement.id = 'fullscreenButton';
    this.fullScreenButtonElement.src = '../images/FullScreen.png';
    this.fullScreenButtonElement.addEventListener('click', this._onClickFullscreenButton.bind(this));
    // 暂时不添加到DOM中
    // this.playerElement.appendChild(this.fullScreenButtonElement);
  }

  /**
   * 添加事件监听器
   * @private
   */
  _addEventListeners() {
    // 视频元数据加载完成事件
    this.videoElement.addEventListener('loadedmetadata', this._onLoadedVideo.bind(this), true);

    // 全屏状态变化事件
    document.addEventListener('webkitfullscreenchange', this._onFullscreenChange.bind(this));
    document.addEventListener('fullscreenchange', this._onFullscreenChange.bind(this));

    // 鼠标点击事件（用于恢复指针锁定）
    this.videoElement.addEventListener('click', this._mouseClick.bind(this), false);

    console.log('事件监听器已添加');
  }

  _onLoadedVideo() {
    this.videoElement.play();
    this.resizeVideo();
  }

  _onClickFullscreenButton() {
    if (!document.fullscreenElement || !document.webkitFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
      } else {
        if (this.playerElement.style.position == 'absolute') {
          this.playerElement.style.position = 'relative';
        } else {
          this.playerElement.style.position = 'absolute';
        }
      }
    }
  }

  _onFullscreenChange() {
    if (document.webkitFullscreenElement || document.fullscreenElement) {
      this.playerElement.style.position = 'absolute';
      this.fullScreenButtonElement.style.display = 'none';

      if (this.lockMouseCheck && this.lockMouseCheck.checked) {
        if (document.webkitFullscreenElement.requestPointerLock) {
          document.webkitFullscreenElement.requestPointerLock();
        } else if (document.fullscreenElement.requestPointerLock) {
          document.fullscreenElement.requestPointerLock();
        } else if (document.mozFullScreenElement.requestPointerLock) {
          document.mozFullScreenElement.requestPointerLock();
        }

        // Subscribe to events
        document.addEventListener('mousemove', this._mouseMove.bind(this), false);
        document.addEventListener('click', this._mouseClickFullScreen.bind(this), false);
      }
    } else {
      this.playerElement.style.position = 'relative';
      this.fullScreenButtonElement.style.display = 'block';

      document.removeEventListener('mousemove', this._mouseMove.bind(this), false);
      document.removeEventListener('click', this._mouseClickFullScreen.bind(this), false);
    }
  }

  _mouseMove(event) {
    // Forward mouseMove event of fullscreen player directly to sender
    // This is required, as the regular mousemove event doesn't fire when in fullscreen mode
    this.sender._onMouseEvent(event);
  }

  _mouseClick() {
    // Restores pointer lock when we unfocus the player and click on it again
    if (this.lockMouseCheck && this.lockMouseCheck.checked) {
      if (this.videoElement.requestPointerLock) {
        this.videoElement.requestPointerLock().catch(function () {});
      }
    }
  }

  _mouseClickFullScreen() {
    // Restores pointer lock when we unfocus the fullscreen player and click on it again
    if (this.lockMouseCheck && this.lockMouseCheck.checked) {
      if (document.webkitFullscreenElement.requestPointerLock) {
        document.webkitFullscreenElement.requestPointerLock();
      } else if (document.fullscreenElement.requestPointerLock) {
        document.fullscreenElement.requestPointerLock();
      } else if (document.mozFullScreenElement.requestPointerLock) {
        document.mozFullScreenElement.requestPointerLock();
      }
    }
  }

  /**
   * 添加视频轨道到媒体流
   * 这是解决画面重影问题的关键方法
   * @param {MediaStreamTrack} track - 要添加的视频轨道
   */
  addTrack(track) {
    if (!this.videoElement || !this.currentMediaStream) {
      console.warn('视频元素或媒体流不存在，无法添加轨道');
      return;
    }

    // 检查轨道类型
    if (track.kind === 'video') {
      console.log('准备添加视频轨道，当前轨道数量:', this.videoTracks.length);

      // 清理现有的视频轨道以避免重影
      this._removeExistingVideoTracks();

      // 强制刷新视频元素以清除缓存的帧
      this._refreshVideoElement();

      // 添加新的视频轨道
      this.currentMediaStream.addTrack(track);
      this.videoTracks.push(track);

      // 监听轨道结束事件
      track.addEventListener('ended', () => {
        console.log('视频轨道已结束');
        this._removeTrackFromArray(track);
      });

      // 强制重新加载视频流以确保新轨道立即生效
      this._reloadVideoStream();

      // 验证轨道添加结果
      this._validateVideoTrackState();

      console.log('视频轨道已添加，当前轨道数量:', this.videoTracks.length);
    } else {
      // 非视频轨道直接添加
      this.currentMediaStream.addTrack(track);
      console.log('已添加非视频轨道:', track.kind);
    }
  }

  /**
   * 验证视频轨道状态
   * 确保没有重复的视频轨道导致重影
   * @private
   */
  _validateVideoTrackState() {
    const streamVideoTracks = this.currentMediaStream.getVideoTracks();
    const managedVideoTracks = this.videoTracks;

    console.log('验证视频轨道状态:');
    console.log('- 媒体流中的视频轨道数量:', streamVideoTracks.length);
    console.log('- 管理的视频轨道数量:', managedVideoTracks.length);

    // 检查是否有不一致的情况
    if (streamVideoTracks.length !== managedVideoTracks.length) {
      console.warn('视频轨道数量不一致！可能存在重影风险');
      console.warn('媒体流轨道:', streamVideoTracks.map(t => t.id));
      console.warn('管理轨道:', managedVideoTracks.map(t => t.id));
    }

    // 检查是否有多个活跃的视频轨道
    const activeTracks = streamVideoTracks.filter(track => track.readyState === 'live');
    if (activeTracks.length > 1) {
      console.error('发现多个活跃的视频轨道，这会导致重影！');
      activeTracks.forEach((track, index) => {
        console.error(`活跃轨道 ${index + 1}:`, track.id, track.label);
      });
    }
  }

  /**
   * 移除现有的视频轨道
   * 防止多个视频轨道同时播放导致重影
   * @private
   */
  _removeExistingVideoTracks() {
    if (this.videoTracks.length > 0) {
      console.log('清理现有视频轨道，数量:', this.videoTracks.length);

      // 先暂停视频播放
      if (this.videoElement && !this.videoElement.paused) {
        this.videoElement.pause();
      }

      // 清理所有现有的视频轨道
      this.videoTracks.forEach((track, index) => {
        console.log(`清理视频轨道 ${index + 1}/${this.videoTracks.length}:`, track.id);

        // 从媒体流中移除轨道
        try {
          this.currentMediaStream.removeTrack(track);
        } catch (error) {
          console.warn('移除轨道失败:', error);
        }

        // 停止轨道
        if (track.readyState === 'live') {
          try {
            track.stop();
            console.log('轨道已停止:', track.id);
          } catch (error) {
            console.warn('停止轨道失败:', error);
          }
        }
      });

      // 清空轨道数组
      this.videoTracks = [];

      // 确保媒体流中没有残留的视频轨道
      const remainingVideoTracks = this.currentMediaStream.getVideoTracks();
      if (remainingVideoTracks.length > 0) {
        console.log('发现残留的视频轨道，强制清理:', remainingVideoTracks.length);
        remainingVideoTracks.forEach(track => {
          this.currentMediaStream.removeTrack(track);
          if (track.readyState === 'live') {
            track.stop();
          }
        });
      }

      console.log('现有视频轨道已彻底清理');
    }
  }

  /**
   * 刷新视频元素以清除缓存的帧
   * 这是解决画面重影问题的关键步骤
   * @private
   */
  _refreshVideoElement() {
    if (!this.videoElement) return;

    console.log('刷新视频元素以清除缓存帧');

    // 方法1: 清空srcObject并强制重新加载
    this.videoElement.srcObject = null;
    this.videoElement.load();

    // 方法2: 强制清除视频缓冲区
    if (this.videoElement.buffered && this.videoElement.buffered.length > 0) {
      try {
        // 尝试清除缓冲区（某些浏览器支持）
        this.videoElement.currentTime = 0;
      } catch (e) {
        console.warn('无法清除视频缓冲区:', e);
      }
    }

    // 方法3: 强制重绘DOM元素
    const display = this.videoElement.style.display;
    this.videoElement.style.display = 'none';
    // 强制重排
    this.videoElement.offsetHeight;
    this.videoElement.style.display = display;
  }

  /**
   * 重新加载视频流以确保新轨道立即生效
   * @private
   */
  _reloadVideoStream() {
    if (!this.videoElement || !this.currentMediaStream) return;

    console.log('重新加载视频流');

    // 使用异步方式确保DOM更新完成后再设置新的媒体流
    setTimeout(() => {
      // 重新设置媒体流以触发重新渲染
      this.videoElement.srcObject = this.currentMediaStream;

      // 监听loadedmetadata事件以确保新轨道已加载
      const onLoadedMetadata = () => {
        console.log('新视频轨道元数据已加载');
        this.videoElement.removeEventListener('loadedmetadata', onLoadedMetadata);

        // 尝试播放（如果之前在播放状态）
        this.videoElement.play().catch(error => {
          console.warn('自动播放失败:', error);
        });

        // 调整视频尺寸以适应新的分辨率
        this.resizeVideo();
      };

      this.videoElement.addEventListener('loadedmetadata', onLoadedMetadata);
    }, 10); // 短暂延迟确保DOM更新完成
  }

  /**
   * 从轨道数组中移除指定轨道
   * @param {MediaStreamTrack} track - 要移除的轨道
   * @private
   */
  _removeTrackFromArray(track) {
    const index = this.videoTracks.indexOf(track);
    if (index > -1) {
      this.videoTracks.splice(index, 1);
      console.log('轨道已从数组中移除，剩余轨道数量:', this.videoTracks.length);
    }
  }

  /**
   * 调整视频尺寸和位置
   * 当窗口大小改变或视频分辨率改变时调用
   * 重新计算视频的缩放比例和位置偏移
   */
  resizeVideo() {
    if (!this.videoElement) {
      console.warn('视频元素不存在，无法调整尺寸');
      return;
    }

    // 检查视频是否有有效的尺寸
    if (this.videoWidth === 0 || this.videoHeight === 0) {
      console.warn('视频尺寸无效，跳过调整');
      return;
    }

    console.log('开始调整视频尺寸，视频分辨率:', this.videoWidth, 'x', this.videoHeight);

    // 获取容器的客户端矩形
    const clientRect = this.videoElement.getBoundingClientRect();
    console.log('容器尺寸:', clientRect.width, 'x', clientRect.height);

    // 计算宽高比
    const videoRatio = this.videoWidth / this.videoHeight;
    const clientRatio = clientRect.width / clientRect.height;

    // 根据宽高比计算缩放比例和偏移量
    if (videoRatio > clientRatio) {
      // 视频更宽，以宽度为准进行缩放
      this._videoScale = clientRect.width / this.videoWidth;
      this._videoOriginX = clientRect.left;
      this._videoOriginY = clientRect.top + (clientRect.height - this.videoHeight * this._videoScale) * 0.5;
    } else {
      // 视频更高，以高度为准进行缩放
      this._videoScale = clientRect.height / this.videoHeight;
      this._videoOriginX = clientRect.left + (clientRect.width - this.videoWidth * this._videoScale) * 0.5;
      this._videoOriginY = clientRect.top;
    }

    console.log('视频尺寸调整完成，缩放比例:', this._videoScale, '偏移量:', this._videoOriginX, this._videoOriginY);

    // 通知输入处理器更新坐标映射
    if (this.sender) {
      this.sender._onResizeEvent();
    }
  }

  get videoWidth() {
    return this.videoElement.videoWidth;
  }

  get videoHeight() {
    return this.videoElement.videoHeight;
  }

  get videoOriginX() {
    return this._videoOriginX;
  }

  get videoOriginY() {
    return this._videoOriginY;
  }

  get videoScale() {
    return this._videoScale;
  }

  /**
   * 删除播放器并清理所有资源
   * 这是解决内存泄漏和画面重影问题的关键方法
   */
  deletePlayer() {
    console.log('开始删除视频播放器');

    // 停止输入处理
    if (this.inputRemoting) {
      this.inputRemoting.stopSending();
      console.log('输入远程控制已停止');
    }

    // 清理视频元素和媒体流
    this._cleanupVideoElement();

    // 清理输入相关对象
    this.inputRemoting = null;
    this.sender = null;
    this.inputSenderChannel = null;

    // 清理DOM元素
    this._cleanupDOMElements();

    // 重置所有属性
    this._resetProperties();

    console.log('视频播放器删除完成');
  }

  /**
   * 清理视频元素和相关资源
   * @private
   */
  _cleanupVideoElement() {
    if (this.videoElement) {
      console.log('开始清理视频元素');

      // 暂停视频播放
      this.videoElement.pause();

      // 清理所有视频轨道
      this._removeExistingVideoTracks();

      // 清理媒体流
      if (this.currentMediaStream) {
        // 停止所有轨道
        this.currentMediaStream.getTracks().forEach(track => {
          track.stop();
          console.log('轨道已停止:', track.kind);
        });

        // 清空媒体流
        this.videoElement.srcObject = null;
        this.currentMediaStream = null;
      }

      // 移除事件监听器
      this._removeEventListeners();

      console.log('视频元素清理完成');
    }
  }

  /**
   * 移除事件监听器
   * @private
   */
  _removeEventListeners() {
    if (this.videoElement) {
      // 克隆节点以移除所有事件监听器
      const newVideoElement = this.videoElement.cloneNode(true);
      this.videoElement.parentNode.replaceChild(newVideoElement, this.videoElement);
      this.videoElement = newVideoElement;
    }

    // 移除全屏事件监听器
    document.removeEventListener('webkitfullscreenchange', this._onFullscreenChange.bind(this));
    document.removeEventListener('fullscreenchange', this._onFullscreenChange.bind(this));

    console.log('事件监听器已移除');
  }

  /**
   * 清理DOM元素
   * @private
   */
  _cleanupDOMElements() {
    if (this.playerElement) {
      // 移除所有子元素
      while (this.playerElement.firstChild) {
        this.playerElement.removeChild(this.playerElement.firstChild);
      }
      console.log('DOM元素已清理');
    }
  }

  /**
   * 重置所有属性
   * @private
   */
  _resetProperties() {
    this.playerElement = null;
    this.lockMouseCheck = null;
    this.videoElement = null;
    this.fullScreenButtonElement = null;
    this.currentMediaStream = null;
    this.videoTracks = [];
    this._videoScale = 1;
    this._videoOriginX = 0;
    this._videoOriginY = 0;

    console.log('播放器属性已重置');
  }

  _isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;
  }

  /**
   * setup datachannel for player input (muouse/keyboard/touch/gamepad)
   * @param {RTCDataChannel} channel
   */
  setupInput(channel) {
    this.sender = new Sender(this.videoElement);
    this.sender.addMouse();
    this.sender.addKeyboard();
    if (this._isTouchDevice()) {
      this.sender.addTouchscreen();
    }
    this.sender.addGamepad();
    this.inputRemoting = new InputRemoting(this.sender);

    this.inputSenderChannel = channel;
    this.inputSenderChannel.onopen = this._onOpenInputSenderChannel.bind(this);
    this.inputRemoting.subscribe(new Observer(this.inputSenderChannel));
  }

  async _onOpenInputSenderChannel() {
    await new Promise(resolve => setTimeout(resolve, 100));
    this.inputRemoting.startSending();
  }
}
