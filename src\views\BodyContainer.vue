<!--
 * @Description: 项目主体
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: tzs
 * @LastEditTime: 2025-06-19 10:43:08
-->
<script setup lang="ts">
import {onMounted} from 'vue';
import {getServerConfig, getRTCConfiguration} from '@/utils/videojs/config.js';
import {VideoPlayer} from '@/utils/videojs/videoplayer.js';
import {RenderStreaming} from '@/utils/videojs/renderstreaming.js';
import {Signaling, WebSocketSignaling} from '@/utils/videojs/signaling.js';

onMounted(() => {
  console.log(new Date().toLocaleString(), 'onMounted');

  /** @type {Element} */
  let playButton;
  /** @type {RenderStreaming} */
  let renderstreaming;
  /** @type {boolean} */
  let useWebSocket;

  const playerDiv = document.getElementById('player');
  const lockMouseCheck = document.getElementById('lockMouseCheck');
  const videoPlayer = new VideoPlayer();

  setup();

  window.document.oncontextmenu = function () {
    return false; // cancel default menu
  };

  window.addEventListener(
    'resize',
    () => {
      console.log('界面大小');

      videoPlayer.resizeVideo();
    },
    true
  );

  window.addEventListener(
    'beforeunload',
    async () => {
      if (!renderstreaming) return;
      await renderstreaming.stop();
    },
    true
  );

  async function setup() {
    const res = await getServerConfig();
    useWebSocket = res.useWebSocket;
    showPlayButton();
  }

  function showPlayButton() {
    if (!document.getElementById('playButton')) {
      const elementPlayButton = document.createElement('img');
      elementPlayButton.id = 'playButton';
      elementPlayButton.src = '../../images/Play.png';
      elementPlayButton.alt = 'Start Streaming';
      playButton = document.getElementById('player').appendChild(elementPlayButton);
      console.log(new Date().toLocaleString(), 'showPlayButton');

      // playButton.addEventListener('click', onClickPlayButton);
      onClickPlayButton();
    }
  }

  function onClickPlayButton() {
    playButton.style.display = 'none';
    videoPlayer.createPlayer(playerDiv, lockMouseCheck);
    setupRenderStreaming();
  }

  async function setupRenderStreaming() {
    const signaling = useWebSocket ? new WebSocketSignaling() : new Signaling();
    const config = getRTCConfiguration();
    renderstreaming = new RenderStreaming(signaling, config);
    renderstreaming.onConnect = onConnect;
    renderstreaming.onDisconnect = onDisconnect;
    renderstreaming.onTrackEvent = data => videoPlayer.addTrack(data.track);
    renderstreaming.onGotOffer = setCodecPreferences;

    await renderstreaming.start();
    await renderstreaming.createConnection();
  }

  function onConnect() {
    const channel = renderstreaming.createDataChannel('input');
    videoPlayer.setupInput(channel);
  }

  async function onDisconnect(connectionId) {
    clearStatsMessage();
    await renderstreaming.stop();
    renderstreaming = null;
    videoPlayer.deletePlayer();
  }

  function setCodecPreferences() {
    /** @type {RTCRtpCodecCapability[] | null} */
    const transceivers = renderstreaming.getTransceivers().filter(t => t.receiver.track.kind == 'video');
  }
});
</script>

<template>
  <div class="body-container">
    <div id="player"></div>
    <div id="message"></div>
  </div>
</template>

<style lang="less">
.body-container {
  height: 100%;

  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  :deep(.crosshair canvas) {
    cursor: crosshair !important;
  }

  #player {
    position: relative;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 100%;
    background-color: #323232;
    align-items: center;
    justify-content: center;

    img {
      width: 200px;
      height: 200px;
    }
  }

  #player::before {
    display: block;
    padding-top: 66%;
    content: '';
  }

  #playButton {
    width: 15%;
    cursor: pointer;
  }

  #Video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  #fullscreenButton {
    position: absolute;
    top: 25px;
    right: 25px;
  }
}
</style>
