<!--
 * @Description: 视频流播放主体组件
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: tzs
 * @LastEditTime: 2025-07-24 15:30:02
 *
 * 功能说明：
 * 1. 管理WebRTC视频流的播放和显示
 * 2. 处理窗口大小变化时的视频调整，解决画面重影问题
 * 3. 提供完整的连接生命周期管理
 * 4. 支持用户输入（鼠标、键盘、触摸）的远程传输
-->
<script setup lang="ts">
import {onMounted, onUnmounted} from 'vue';
import {getServerConfig, getRTCConfiguration} from '@/utils/videojs/config.js';
import {VideoPlayer} from '@/utils/videojs/videoplayer.js';
import {RenderStreaming} from '@/utils/videojs/renderstreaming.js';
import {Signaling, WebSocketSignaling} from '@/utils/videojs/signaling.js';

// 全局变量定义
/** @type {Element} */
let playButton;
/** @type {RenderStreaming} */
let renderstreaming;
/** @type {boolean} */
let useWebSocket;
/** @type {VideoPlayer} */
let videoPlayer;

// 防抖定时器，用于优化窗口大小调整
let resizeTimer = null;

/**
 * 组件挂载时的初始化逻辑
 */
onMounted(() => {
  console.log(new Date().toLocaleString(), '视频流组件开始挂载');

  // 创建视频播放器实例
  videoPlayer = new VideoPlayer();

  // 初始化配置
  setup();

  // 禁用右键菜单
  window.document.oncontextmenu = function () {
    return false; // 取消默认右键菜单
  };

  // 添加窗口大小变化监听器（使用防抖优化）
  window.addEventListener('resize', handleWindowResize, true);

  // 添加页面卸载前的清理监听器
  window.addEventListener('beforeunload', handleBeforeUnload, true);

  console.log('视频流组件挂载完成');
});

/**
 * 组件卸载时的清理逻辑
 */
onUnmounted(() => {
  console.log('视频流组件开始卸载');

  // 移除事件监听器
  window.removeEventListener('resize', handleWindowResize, true);
  window.removeEventListener('beforeunload', handleBeforeUnload, true);

  // 清理资源
  cleanup();

  console.log('视频流组件卸载完成');
});

/**
 * 处理窗口大小变化事件（防抖优化）
 * 解决频繁调整窗口大小时的性能问题
 */
function handleWindowResize() {
  // 清除之前的定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  // 设置新的定时器，300ms后执行调整
  resizeTimer = setTimeout(() => {
    console.log('窗口大小已改变，开始调整视频尺寸');

    if (videoPlayer && videoPlayer.videoElement) {
      videoPlayer.resizeVideo();
      console.log('视频尺寸调整完成');
    }
  }, 300);
}

/**
 * 处理页面卸载前事件
 * 确保在页面关闭前正确清理WebRTC连接
 */
async function handleBeforeUnload() {
  console.log('页面即将卸载，开始清理资源');
  await cleanup();
}

/**
 * 清理所有资源
 * 统一的资源清理函数
 */
async function cleanup() {
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
    resizeTimer = null;
  }

  // 停止渲染流
  if (renderstreaming) {
    await renderstreaming.stop();
    renderstreaming = null;
    console.log('渲染流已停止');
  }

  // 删除视频播放器
  if (videoPlayer) {
    videoPlayer.deletePlayer();
    videoPlayer = null;
    console.log('视频播放器已删除');
  }
}

/**
 * 异步初始化配置
 * 获取服务器配置并显示播放按钮
 */
async function setup() {
  try {
    console.log('开始获取服务器配置');
    const res = await getServerConfig();
    useWebSocket = res.useWebSocket;
    console.log('服务器配置获取成功，使用WebSocket:', useWebSocket);

    renderPlayer();
  } catch (error) {
    console.error('获取服务器配置失败:', error);
  }
}

/**
 * 播放按钮点击处理函数
 * 隐藏播放按钮并开始初始化视频播放器和渲染流
 */
function renderPlayer() {
  console.log('开始初始化视频播放');

  // 获取必要的DOM元素
  const playerDiv = document.getElementById('player');
  const lockMouseCheck = document.getElementById('lockMouseCheck');

  if (playerDiv) {
    // 创建视频播放器
    videoPlayer.createPlayer(playerDiv, lockMouseCheck);

    // 设置渲染流
    setupRenderStreaming();
  } else {
    console.error('找不到播放器容器，无法创建视频播放器');
  }
}

/**
 * 设置WebRTC渲染流
 * 创建信令连接、配置回调函数并启动连接
 */
async function setupRenderStreaming() {
  try {
    console.log('开始设置WebRTC渲染流');

    // 创建信令实例
    const signaling = useWebSocket ? new WebSocketSignaling() : new Signaling();
    const config = getRTCConfiguration();

    // 创建渲染流实例
    renderstreaming = new RenderStreaming(signaling, config);

    // 配置回调函数
    renderstreaming.onConnect = onConnect;
    renderstreaming.onDisconnect = onDisconnect;
    renderstreaming.onTrackEvent = data => {
      console.log('收到视频轨道事件');
      videoPlayer.addTrack(data.track);
    };
    renderstreaming.onGotOffer = setCodecPreferences;

    // 启动连接
    await renderstreaming.start();
    await renderstreaming.createConnection();

    console.log('WebRTC渲染流设置完成');
  } catch (error) {
    console.error('设置WebRTC渲染流失败:', error);
  }
}

/**
 * WebRTC连接建立成功回调
 * 创建输入数据通道并设置输入处理
 */
function onConnect() {
  console.log('WebRTC连接已建立');

  try {
    // 创建输入数据通道
    const channel = renderstreaming.createDataChannel('input');

    // 设置视频播放器的输入处理
    if (videoPlayer && channel) {
      videoPlayer.setupInput(channel);
      console.log('输入数据通道已设置');
    }
  } catch (error) {
    console.error('设置输入数据通道失败:', error);
  }
}

/**
 * 清理统计信息显示
 * 在连接断开时清除页面上显示的统计信息
 */
function clearStatsMessage() {
  const messageElement = document.getElementById('message');
  if (messageElement) {
    messageElement.innerHTML = '';
    console.log('统计信息已清理');
  }
}

/**
 * WebRTC连接断开处理函数
 * 负责清理所有相关资源，包括统计信息、渲染流和视频播放器
 * @param {string} connectionId - 连接ID
 */
async function onDisconnect(connectionId) {
  console.log('开始断开连接处理，连接ID:', connectionId);

  // 清理统计信息显示
  clearStatsMessage();

  // 停止渲染流并清理资源
  if (renderstreaming) {
    await renderstreaming.stop();
    renderstreaming = null;
    console.log('渲染流已停止并清理');
  }

  // 删除视频播放器并清理所有相关元素
  if (videoPlayer) {
    videoPlayer.deletePlayer();
    console.log('视频播放器已删除');
  }
}

/**
 * 设置编解码器偏好
 * 用于优化视频流的编解码器选择
 */
function setCodecPreferences() {
  if (!renderstreaming) return;

  /** @type {RTCRtpCodecCapability[] | null} */
  const transceivers = renderstreaming.getTransceivers().filter(t => t.receiver.track.kind === 'video');
  console.log('视频转换器数量:', transceivers.length);
}
</script>

<template>
  <div class="body-container">
    <div id="player"></div>
    <div id="message"></div>
  </div>
</template>

<style lang="less">
.body-container {
  height: 100%;

  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  :deep(.crosshair canvas) {
    cursor: crosshair !important;
  }

  #player {
    position: relative;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 100%;
    background-color: #323232;
    align-items: center;
    justify-content: center;

    img {
      width: 200px;
      height: 200px;
    }
  }

  #player::before {
    display: block;
    padding-top: 66%;
    content: '';
  }

  #playButton {
    width: 15%;
    cursor: pointer;
  }

  #Video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  #fullscreenButton {
    position: absolute;
    top: 25px;
    right: 25px;
  }
}
</style>
