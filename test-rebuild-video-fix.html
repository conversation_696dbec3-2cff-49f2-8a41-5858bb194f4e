<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC视频轨道重影修复测试 - 完全重建方案</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            width: 100%;
            height: 400px;
            background: #000;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
            position: relative;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC视频轨道重影修复测试 - 完全重建方案</h1>
        
        <div class="success">
            <strong>修复方案说明：</strong>
            <p>基于"页面刷新能解决重影问题"的关键发现，我们实现了完全重建机制：</p>
            <ul>
                <li>✅ 完全清理现有视频元素和媒体流</li>
                <li>✅ 重新创建全新的视频元素和媒体流</li>
                <li>✅ 模拟页面刷新的完整重建过程</li>
                <li>✅ 彻底解决浏览器缓存和状态残留问题</li>
            </ul>
        </div>

        <div class="video-container" id="videoContainer">
            <!-- 视频播放器将在这里创建 -->
        </div>

        <div class="controls">
            <button onclick="testTrackSwitch('1920x1080')">测试1920x1080轨道</button>
            <button onclick="testTrackSwitch('1280x720')">测试1280x720轨道</button>
            <button onclick="testTrackSwitch('640x480')">测试640x480轨道</button>
            <button onclick="clearLogs()">清除日志</button>
            <button onclick="validateState()">验证状态</button>
        </div>

        <div class="status">
            <h3>当前状态</h3>
            <div id="statusInfo">
                <p>视频轨道数量: <span id="trackCount">0</span></p>
                <p>媒体流状态: <span id="streamStatus">未初始化</span></p>
                <p>视频元素ID: <span id="videoElementId">无</span></p>
                <p>最后操作: <span id="lastAction">无</span></p>
            </div>
        </div>

        <div class="log" id="logContainer">
            <div>测试日志将在这里显示...</div>
        </div>
    </div>

    <script>
        // 模拟完全重建的VideoPlayer
        class RebuildVideoPlayer {
            constructor() {
                this.playerElement = document.getElementById('videoContainer');
                this.videoElement = null;
                this.currentMediaStream = null;
                this.videoTracks = [];
                
                this._createVideoElement();
                this.log('RebuildVideoPlayer实例已创建');
            }

            _createVideoElement() {
                this.videoElement = document.createElement('video');
                this.videoElement.id = 'Video-' + Date.now();
                this.videoElement.style.width = '100%';
                this.videoElement.style.height = '100%';
                this.videoElement.style.objectFit = 'contain';
                this.videoElement.autoplay = true;
                this.videoElement.playsInline = true;
                this.videoElement.muted = true;

                this.currentMediaStream = new MediaStream();
                this.videoElement.srcObject = this.currentMediaStream;
                this.playerElement.appendChild(this.videoElement);
                
                this.log('视频元素已创建: ' + this.videoElement.id);
                this.updateStatus();
            }

            // 核心修复方法：完全重建机制
            addTrack(track) {
                if (track.kind === 'video') {
                    this.log(`准备添加视频轨道，使用完全重建机制`);
                    this._rebuildVideoElementForNewTrack(track);
                } else {
                    this.currentMediaStream.addTrack(track);
                    this.log(`已添加非视频轨道: ${track.kind}`);
                }
            }

            _rebuildVideoElementForNewTrack(track) {
                this.log('开始完全重建视频元素以添加新轨道');
                
                // 保存当前状态
                const wasPlaying = this.videoElement && !this.videoElement.paused;

                // 1. 完全清理
                this._completeCleanup();

                // 2. 重新创建
                this._recreateVideoElement();

                // 3. 添加新轨道
                this.currentMediaStream.addTrack(track);
                this.videoTracks.push(track);

                // 4. 监听轨道结束
                track.addEventListener('ended', () => {
                    this.log('视频轨道已结束');
                    this._removeTrackFromArray(track);
                });

                // 5. 恢复播放状态
                if (wasPlaying) {
                    this.videoElement.play().catch(error => {
                        this.log('恢复播放失败: ' + error.message, 'warn');
                    });
                }

                // 6. 验证结果
                this._validateVideoTrackState();
                
                this.log('视频元素重建完成，新轨道已添加');
                this.updateStatus();
            }

            _completeCleanup() {
                this.log('开始完全清理视频元素和媒体流');

                if (this.videoElement) {
                    // 暂停播放
                    this.videoElement.pause();

                    // 清理媒体流
                    if (this.currentMediaStream) {
                        this.currentMediaStream.getTracks().forEach(track => {
                            track.stop();
                        });
                        this.currentMediaStream = null;
                    }

                    // 清空srcObject
                    this.videoElement.srcObject = null;
                    this.videoElement.load();

                    // 从DOM中移除
                    if (this.videoElement.parentNode) {
                        this.videoElement.parentNode.removeChild(this.videoElement);
                    }

                    this.videoElement = null;
                }

                // 清空轨道数组
                this.videoTracks = [];
                this.log('完全清理完成');
            }

            _recreateVideoElement() {
                this.log('重新创建视频元素和媒体流');

                // 创建全新的视频元素
                this.videoElement = document.createElement('video');
                this.videoElement.id = 'Video-' + Date.now();
                this.videoElement.style.width = '100%';
                this.videoElement.style.height = '100%';
                this.videoElement.style.objectFit = 'contain';
                this.videoElement.autoplay = true;
                this.videoElement.playsInline = true;
                this.videoElement.muted = true;

                // 创建全新的媒体流
                this.currentMediaStream = new MediaStream();
                this.videoElement.srcObject = this.currentMediaStream;

                // 添加到DOM
                if (this.playerElement) {
                    this.playerElement.appendChild(this.videoElement);
                }

                this.log('视频元素和媒体流重新创建完成: ' + this.videoElement.id);
            }

            _validateVideoTrackState() {
                const streamVideoTracks = this.currentMediaStream.getVideoTracks();
                const managedVideoTracks = this.videoTracks;
                
                this.log('验证视频轨道状态:');
                this.log(`- 媒体流中的视频轨道数量: ${streamVideoTracks.length}`);
                this.log(`- 管理的视频轨道数量: ${managedVideoTracks.length}`);
                
                if (streamVideoTracks.length !== managedVideoTracks.length) {
                    this.log('视频轨道数量不一致！', 'error');
                }
                
                const activeTracks = streamVideoTracks.filter(track => track.readyState === 'live');
                if (activeTracks.length > 1) {
                    this.log('发现多个活跃的视频轨道，这会导致重影！', 'error');
                } else if (activeTracks.length === 1) {
                    this.log('✓ 只有一个活跃的视频轨道，状态正常', 'success');
                }
            }

            _removeTrackFromArray(track) {
                const index = this.videoTracks.indexOf(track);
                if (index > -1) {
                    this.videoTracks.splice(index, 1);
                    this.log('轨道已从数组中移除');
                    this.updateStatus();
                }
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                const color = type === 'warn' ? '#ffff00' : type === 'error' ? '#ff0000' : type === 'success' ? '#00ff00' : '#00ff00';
                
                const logEntry = document.createElement('div');
                logEntry.style.color = color;
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                console.log(`[RebuildVideoPlayer] ${message}`);
            }

            updateStatus() {
                document.getElementById('trackCount').textContent = this.videoTracks.length;
                document.getElementById('streamStatus').textContent = this.currentMediaStream ? '已初始化' : '未初始化';
                document.getElementById('videoElementId').textContent = this.videoElement ? this.videoElement.id : '无';
            }
        }

        // 创建测试实例
        const testPlayer = new RebuildVideoPlayer();

        // 创建模拟视频轨道
        function createMockVideoTrack(resolution) {
            const canvas = document.createElement('canvas');
            const [width, height] = resolution.split('x').map(Number);
            canvas.width = width;
            canvas.height = height;
            
            const ctx = canvas.getContext('2d');
            
            const colors = {
                '1920x1080': '#ff0000',
                '1280x720': '#00ff00', 
                '640x480': '#0000ff'
            };
            
            ctx.fillStyle = colors[resolution] || '#ffffff';
            ctx.fillRect(0, 0, width, height);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(resolution, width/2, height/2);
            
            const stream = canvas.captureStream(30);
            const track = stream.getVideoTracks()[0];
            
            track.id = `video-track-${resolution}-${Date.now()}`;
            track.label = `Mock Video Track ${resolution}`;
            
            return track;
        }

        // 全局测试函数
        window.testTrackSwitch = function(resolution) {
            const track = createMockVideoTrack(resolution);
            testPlayer.log(`开始测试 ${resolution} 视频轨道切换`);
            testPlayer.addTrack(track);
            document.getElementById('lastAction').textContent = `切换到 ${resolution}`;
        };

        window.clearLogs = function() {
            document.getElementById('logContainer').innerHTML = '<div>日志已清除...</div>';
        };

        window.validateState = function() {
            testPlayer._validateVideoTrackState();
        };
    </script>
</body>
</html>
